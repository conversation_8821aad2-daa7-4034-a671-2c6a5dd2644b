package com.BE.repository;

import com.BE.model.entity.ResourceNodeLink;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
public interface ResourceNodeLinkRepository extends JpaRepository<ResourceNodeLink, Long> {

    // Find by resource and node
    Optional<ResourceNodeLink> findByResourceIdAndNodeId(Long resourceId, Long nodeId);

    // Find all nodes for a resource
    List<ResourceNodeLink> findByResourceId(Long resourceId);

    // Find all resources for a node
    List<ResourceNodeLink> findByNodeId(Long nodeId);

    // Find resources for a lesson (through nodes)
    @Query("SELECT rnl FROM ResourceNodeLink rnl WHERE rnl.node.lesson.id = :lessonId")
    List<ResourceNodeLink> findByLessonId(@Param("lessonId") Long lessonId);

    // Delete by resource and node
    @Modifying
    @Transactional
    @Query("DELETE FROM ResourceNodeLink rnl WHERE rnl.resource.id = :resourceId AND rnl.node.id = :nodeId")
    void deleteByResourceIdAndNodeId(@Param("resourceId") Long resourceId, @Param("nodeId") Long nodeId);

    // Delete all links for a resource
    @Modifying
    @Transactional
    void deleteByResourceId(Long resourceId);

    // Delete all links for a node
    @Modifying
    @Transactional
    void deleteByNodeId(Long nodeId);

    // Count nodes for a resource
    long countByResourceId(Long resourceId);

    // Count resources for a node
    long countByNodeId(Long nodeId);

    // Check if resource is used in any node
    boolean existsByResourceId(Long resourceId);

    // Check if node has any resources
    boolean existsByNodeId(Long nodeId);
}
