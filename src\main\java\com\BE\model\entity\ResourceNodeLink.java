package com.BE.model.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Entity
@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "resource_node_link")
public class ResourceNodeLink {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;

    @ManyToOne
    @JoinColumn(name = "resource_id", nullable = false)
    @JsonIgnore
    Resource resource;

    @ManyToOne
    @JoinColumn(name = "node_id", nullable = false)
    @JsonIgnore
    LessonPlanNode node;

    @Lob
    @Column(columnDefinition = "TEXT")
    String note;

    String createdAt;
}
