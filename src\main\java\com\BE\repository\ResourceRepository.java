package com.BE.repository;

import com.BE.enums.ResourceTypeEnum;
import com.BE.enums.StatusEnum;
import com.BE.model.entity.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ResourceRepository extends JpaRepository<Resource, Long> {

    // Find resource by name and creator (to prevent duplicates)
    Optional<Resource> findByNameAndCreatedById(String name, UUID createdById);

    // Find resource by name, creator and exclude specific ID (for updates)
    Optional<Resource> findByNameAndCreatedByIdAndIdNot(String name, UUID createdById, Long id);

    // Advanced search with filters
    @Query("SELECT r FROM Resource r WHERE " +
            "(:search IS NULL OR LOWER(r.name) LIKE LOWER(CONCAT('%', :search, '%')) OR LOWER(r.description) LIKE LOWER(CONCAT('%', :search, '%'))) AND " +
            "(:type IS NULL OR r.type = :type) AND " +
            "(:status IS NULL OR r.status = :status) AND " +
            "(:createdBy IS NULL OR r.createdBy.id = :createdBy)")
    Page<Resource> findResourcesWithFilters(
            @Param("search") String search,
            @Param("type") ResourceTypeEnum type,
            @Param("status") StatusEnum status,
            @Param("createdBy") UUID createdBy,
            Pageable pageable);

    // Find resources by tag
    @Query("SELECT DISTINCT r FROM Resource r JOIN r.resourceTags rt WHERE rt.tag.id = :tagId AND r.status = :status")
    Page<Resource> findByTagId(@Param("tagId") Long tagId, @Param("status") StatusEnum status, Pageable pageable);

    // Find resources by multiple tags
    @Query("SELECT DISTINCT r FROM Resource r JOIN r.resourceTags rt WHERE rt.tag.id IN :tagIds AND r.status = :status")
    Page<Resource> findByTagIds(@Param("tagIds") List<Long> tagIds, @Param("status") StatusEnum status, Pageable pageable);

    // Find unused resources (not linked to any node)
    @Query("SELECT r FROM Resource r WHERE r.nodeLinks IS EMPTY AND r.status = :status")
    Page<Resource> findUnusedResources(@Param("status") StatusEnum status, Pageable pageable);

    // Find resources used in specific lesson
    @Query("SELECT DISTINCT r FROM Resource r JOIN r.nodeLinks rnl JOIN rnl.node n WHERE n.lesson.id = :lessonId AND r.status = :status")
    Page<Resource> findByLessonId(@Param("lessonId") Long lessonId, @Param("status") StatusEnum status, Pageable pageable);

    // Count resources by type
    @Query("SELECT r.type, COUNT(r) FROM Resource r WHERE r.status = :status GROUP BY r.type")
    List<Object[]> countResourcesByType(@Param("status") StatusEnum status);

    // Find resources by creator
    Page<Resource> findByCreatedByIdAndStatus(UUID createdById, StatusEnum status, Pageable pageable);

    // Find resources by type
    Page<Resource> findByTypeAndStatus(ResourceTypeEnum type, StatusEnum status, Pageable pageable);
}
