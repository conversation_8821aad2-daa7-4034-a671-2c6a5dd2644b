package com.BE.model.request;

import com.BE.model.entity.LessonPlanNode;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LessonPlanNodeRequest {

    @NotNull(message = "Lesson ID is required")
    Long lessonId;

    Long parentId; // Optional - null for root nodes

    @NotNull(message = "Node type is required")
    LessonPlanNode.NodeTypeEnum type;

    @NotBlank(message = "Title is required")
    @Size(max = 255, message = "Title must not exceed 255 characters")
    String title;

    @Size(max = 5000, message = "Content must not exceed 5000 characters")
    String content;

    Integer orderIndex; // Optional - will be auto-calculated if not provided
}
