package com.BE.service.implementServices;

import com.BE.enums.ResourceTypeEnum;
import com.BE.enums.StatusEnum;
import com.BE.exception.exceptions.BadRequestException;
import com.BE.exception.exceptions.NotFoundException;
import com.BE.mapper.ResourceMapper;
import com.BE.model.dto.FileUploadResult;
import com.BE.model.entity.*;
import com.BE.model.request.ResourceRequest;
import com.BE.model.request.ResourceUpdateRequest;
import com.BE.model.response.ResourceResponse;
import com.BE.repository.*;
import com.BE.service.FileUploadService;
import com.BE.service.interfaceServices.IResourceService;
import com.BE.utils.AccountUtils;
import com.BE.utils.DateNowUtils;
import com.BE.utils.PageUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class ResourceServiceImpl implements IResourceService {

    ResourceRepository resourceRepository;
    TagRepository tagRepository;
    ResourceTagRepository resourceTagRepository;
    ResourceNodeLinkRepository resourceNodeLinkRepository;
    LessonPlanNodeRepository lessonPlanNodeRepository;
    UserRepository userRepository;

    ResourceMapper resourceMapper;
    FileUploadService fileUploadService;
    AccountUtils accountUtils;
    DateNowUtils dateNowUtils;
    PageUtil pageUtil;

    @Override
    @Transactional
    public ResourceResponse uploadResource(MultipartFile file, ResourceRequest request) {
        // Get current user
        User currentUser = accountUtils.getCurrentUser();

        // Check for duplicate name
        if (resourceRepository.findByNameAndCreatedById(request.getName().trim(), currentUser.getId()).isPresent()) {
            throw new BadRequestException("Resource with name '" + request.getName() + "' already exists");
        }

        // Upload file
        FileUploadResult uploadResult = fileUploadService.uploadFile(file, "resources");

        // Determine resource type from file
        ResourceTypeEnum resourceType = determineResourceType(uploadResult.getMimeType());

        // Create resource entity
        Resource resource = new Resource();
        resource.setType(resourceType);
        resource.setName(request.getName().trim());
        resource.setDescription(request.getDescription());
        resource.setUrl(uploadResult.getPublicUrl());
        resource.setFileSize(uploadResult.getFileSize());
        resource.setMimeType(uploadResult.getMimeType());
        resource.setOriginalFilename(uploadResult.getOriginalFilename());
        resource.setCreatedBy(currentUser);
        resource.setStatus(StatusEnum.ACTIVE);
        resource.setCreatedAt(dateNowUtils.dateNow());
        resource.setUpdatedAt(dateNowUtils.dateNow());

        // Save resource
        resource = resourceRepository.save(resource);

        // Add tags if provided
        if (request.getTagIds() != null && !request.getTagIds().isEmpty()) {
            addTagsToResourceInternal(resource, request.getTagIds());
        }

        return resourceMapper.toResourceResponse(resource);
    }

    @Override
    @Transactional
    public ResourceResponse createExternalResource(ResourceRequest request) {
        // Get current user
        User currentUser = accountUtils.getCurrentUser();

        // Check for duplicate name
        if (resourceRepository.findByNameAndCreatedById(request.getName().trim(), currentUser.getId()).isPresent()) {
            throw new BadRequestException("Resource with name '" + request.getName() + "' already exists");
        }

        // Validate external URL
        if (request.getUrl() == null || request.getUrl().trim().isEmpty()) {
            throw new BadRequestException("URL is required for external resources");
        }

        // Create resource entity
        Resource resource = new Resource();
        resource.setType(ResourceTypeEnum.IFRAME);
        resource.setName(request.getName().trim());
        resource.setDescription(request.getDescription());
        resource.setUrl(request.getUrl().trim());
        resource.setCreatedBy(currentUser);
        resource.setStatus(StatusEnum.ACTIVE);
        resource.setCreatedAt(dateNowUtils.dateNow());
        resource.setUpdatedAt(dateNowUtils.dateNow());

        // Save resource
        resource = resourceRepository.save(resource);

        // Add tags if provided
        if (request.getTagIds() != null && !request.getTagIds().isEmpty()) {
            addTagsToResourceInternal(resource, request.getTagIds());
        }

        return resourceMapper.toResourceResponse(resource);
    }

    @Override
    public Page<ResourceResponse> getAllResources(int page, int size, String search, ResourceTypeEnum type,
            StatusEnum status, UUID createdBy, String sortBy, String sortDirection) {
        Pageable pageable = pageUtil.getPageable(page, size, sortBy, sortDirection);

        Page<Resource> resources = resourceRepository.findResourcesWithFilters(
                search, type, status != null ? status : StatusEnum.ACTIVE, createdBy, pageable);

        return resources.map(resourceMapper::toResourceResponse);
    }

    @Override
    public ResourceResponse getResourceById(Long id) {
        Resource resource = resourceRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Resource not found with ID: " + id));

        return resourceMapper.toResourceResponse(resource);
    }

    @Override
    @Transactional
    public ResourceResponse updateResource(Long id, ResourceUpdateRequest request) {
        Resource resource = resourceRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Resource not found with ID: " + id));

        // Check for duplicate name (excluding current resource)
        if (resourceRepository.findByNameAndCreatedByIdAndIdNot(
                request.getName().trim(), resource.getCreatedBy().getId(), id).isPresent()) {
            throw new BadRequestException("Resource with name '" + request.getName() + "' already exists");
        }

        // Update fields
        resource.setName(request.getName().trim());
        resource.setDescription(request.getDescription());
        resource.setUpdatedAt(dateNowUtils.dateNow());

        // Update URL for external resources
        if (resource.getType() == ResourceTypeEnum.IFRAME && request.getUrl() != null) {
            resource.setUrl(request.getUrl().trim());
        }

        resource = resourceRepository.save(resource);

        return resourceMapper.toResourceResponse(resource);
    }

    private ResourceTypeEnum determineResourceType(String mimeType) {
        if (mimeType == null)
            return ResourceTypeEnum.IMAGE;

        if (mimeType.startsWith("image/")) {
            if (mimeType.equals("image/gif"))
                return ResourceTypeEnum.GIF;
            if (mimeType.equals("image/webp"))
                return ResourceTypeEnum.WEBP;
            return ResourceTypeEnum.IMAGE;
        } else if (mimeType.startsWith("video/")) {
            return ResourceTypeEnum.VIDEO;
        }

        return ResourceTypeEnum.IMAGE;
    }

    private void addTagsToResourceInternal(Resource resource, List<Long> tagIds) {
        for (Long tagId : tagIds) {
            Tag tag = tagRepository.findById(tagId)
                    .orElseThrow(() -> new NotFoundException("Tag not found with ID: " + tagId));

            // Check if relationship already exists
            if (resourceTagRepository.findByResourceIdAndTagId(resource.getId(), tagId).isEmpty()) {
                ResourceTag resourceTag = new ResourceTag();
                resourceTag.setResource(resource);
                resourceTag.setTag(tag);
                resourceTag.setCreatedAt(dateNowUtils.dateNow());
                resourceTagRepository.save(resourceTag);
            }
        }
    }

    @Override
    @Transactional
    public ResourceResponse updateResourceFile(Long id, MultipartFile file) {
        Resource resource = resourceRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Resource not found with ID: " + id));

        // Only allow file updates for uploaded resources (not external links)
        if (resource.getType() == ResourceTypeEnum.IFRAME) {
            throw new BadRequestException("Cannot update file for external resources");
        }

        // Upload new file
        FileUploadResult uploadResult = fileUploadService.uploadFile(file, "resources");

        // Delete old file if it exists
        if (resource.getUrl() != null && !resource.getUrl().startsWith("http")) {
            // Extract path from URL for deletion
            String oldPath = extractPathFromUrl(resource.getUrl());
            fileUploadService.deleteFile(oldPath);
        }

        // Update resource with new file info
        ResourceTypeEnum newType = determineResourceType(uploadResult.getMimeType());
        resource.setType(newType);
        resource.setUrl(uploadResult.getPublicUrl());
        resource.setFileSize(uploadResult.getFileSize());
        resource.setMimeType(uploadResult.getMimeType());
        resource.setOriginalFilename(uploadResult.getOriginalFilename());
        resource.setUpdatedAt(dateNowUtils.dateNow());

        resource = resourceRepository.save(resource);
        return resourceMapper.toResourceResponse(resource);
    }

    @Override
    @Transactional
    public ResourceResponse changeResourceStatus(Long id, StatusEnum status) {
        Resource resource = resourceRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Resource not found with ID: " + id));

        resource.setStatus(status);
        resource.setUpdatedAt(dateNowUtils.dateNow());

        resource = resourceRepository.save(resource);
        return resourceMapper.toResourceResponse(resource);
    }

    @Override
    @Transactional
    public void deleteResource(Long id) {
        Resource resource = resourceRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Resource not found with ID: " + id));

        // Check if resource is used in any nodes
        if (resourceNodeLinkRepository.existsByResourceId(id)) {
            throw new BadRequestException(
                    "Cannot delete resource that is linked to lesson plan nodes. Please unlink first.");
        }

        // Delete file from storage if it's an uploaded file
        if (resource.getType() != ResourceTypeEnum.IFRAME && resource.getUrl() != null) {
            String path = extractPathFromUrl(resource.getUrl());
            fileUploadService.deleteFile(path);
        }

        // Delete resource (cascade will handle tags and links)
        resourceRepository.delete(resource);
    }

    @Override
    public Page<ResourceResponse> getResourcesByTag(Long tagId, int page, int size, StatusEnum status, String sortBy,
            String sortDirection) {
        Pageable pageable = PageUtil.createPageable(page, size, sortBy, sortDirection);
        Page<Resource> resources = resourceRepository.findByTagId(tagId, status != null ? status : StatusEnum.ACTIVE,
                pageable);
        return resources.map(resourceMapper::toResourceResponse);
    }

    @Override
    public Page<ResourceResponse> getResourcesByTags(List<Long> tagIds, int page, int size, StatusEnum status,
            String sortBy, String sortDirection) {
        Pageable pageable = PageUtil.createPageable(page, size, sortBy, sortDirection);
        Page<Resource> resources = resourceRepository.findByTagIds(tagIds, status != null ? status : StatusEnum.ACTIVE,
                pageable);
        return resources.map(resourceMapper::toResourceResponse);
    }

    @Override
    public Page<ResourceResponse> getUnusedResources(int page, int size, StatusEnum status, String sortBy,
            String sortDirection) {
        Pageable pageable = PageUtil.createPageable(page, size, sortBy, sortDirection);
        Page<Resource> resources = resourceRepository.findUnusedResources(status != null ? status : StatusEnum.ACTIVE,
                pageable);
        return resources.map(resourceMapper::toResourceResponse);
    }

    @Override
    public Page<ResourceResponse> getResourcesByLesson(Long lessonId, int page, int size, StatusEnum status,
            String sortBy, String sortDirection) {
        Pageable pageable = PageUtil.createPageable(page, size, sortBy, sortDirection);
        Page<Resource> resources = resourceRepository.findByLessonId(lessonId,
                status != null ? status : StatusEnum.ACTIVE, pageable);
        return resources.map(resourceMapper::toResourceResponse);
    }

    @Override
    public Page<ResourceResponse> getResourcesByCreator(UUID createdBy, int page, int size, StatusEnum status,
            String sortBy, String sortDirection) {
        Pageable pageable = PageUtil.createPageable(page, size, sortBy, sortDirection);
        Page<Resource> resources = resourceRepository.findByCreatedByIdAndStatus(createdBy,
                status != null ? status : StatusEnum.ACTIVE, pageable);
        return resources.map(resourceMapper::toResourceResponse);
    }

    @Override
    public List<Object[]> getResourceStatistics() {
        return resourceRepository.countResourcesByType(StatusEnum.ACTIVE);
    }

    @Override
    @Transactional
    public ResourceResponse addTagsToResource(Long resourceId, List<Long> tagIds) {
        Resource resource = resourceRepository.findById(resourceId)
                .orElseThrow(() -> new NotFoundException("Resource not found with ID: " + resourceId));

        addTagsToResourceInternal(resource, tagIds);

        return resourceMapper.toResourceResponse(resource);
    }

    @Override
    @Transactional
    public ResourceResponse removeTagsFromResource(Long resourceId, List<Long> tagIds) {
        Resource resource = resourceRepository.findById(resourceId)
                .orElseThrow(() -> new NotFoundException("Resource not found with ID: " + resourceId));

        for (Long tagId : tagIds) {
            resourceTagRepository.deleteByResourceIdAndTagId(resourceId, tagId);
        }

        return resourceMapper.toResourceResponse(resource);
    }

    @Override
    @Transactional
    public void linkResourceToNode(Long resourceId, Long nodeId, String note) {
        Resource resource = resourceRepository.findById(resourceId)
                .orElseThrow(() -> new NotFoundException("Resource not found with ID: " + resourceId));

        LessonPlanNode node = lessonPlanNodeRepository.findById(nodeId)
                .orElseThrow(() -> new NotFoundException("Lesson plan node not found with ID: " + nodeId));

        // Check if link already exists
        if (resourceNodeLinkRepository.findByResourceIdAndNodeId(resourceId, nodeId).isPresent()) {
            throw new BadRequestException("Resource is already linked to this node");
        }

        ResourceNodeLink link = new ResourceNodeLink();
        link.setResource(resource);
        link.setNode(node);
        link.setNote(note);
        link.setCreatedAt(dateNowUtils.dateNow());

        resourceNodeLinkRepository.save(link);
    }

    @Override
    @Transactional
    public void unlinkResourceFromNode(Long resourceId, Long nodeId) {
        if (!resourceNodeLinkRepository.findByResourceIdAndNodeId(resourceId, nodeId).isPresent()) {
            throw new NotFoundException("Resource link not found");
        }

        resourceNodeLinkRepository.deleteByResourceIdAndNodeId(resourceId, nodeId);
    }

    @Override
    public List<Object> getResourceUsage(Long resourceId) {
        Resource resource = resourceRepository.findById(resourceId)
                .orElseThrow(() -> new NotFoundException("Resource not found with ID: " + resourceId));

        List<ResourceNodeLink> links = resourceNodeLinkRepository.findByResourceId(resourceId);

        return links.stream()
                .map(link -> {
                    LessonPlanNode node = link.getNode();
                    return new Object[] {
                            node.getId(),
                            node.getTitle(),
                            node.getLesson().getId(),
                            node.getLesson().getName(),
                            link.getNote(),
                            link.getCreatedAt()
                    };
                })
                .collect(java.util.stream.Collectors.toList());
    }

    private String extractPathFromUrl(String url) {
        // Extract the path from Supabase public URL
        // URL format: https://xxx.supabase.co/storage/v1/object/public/bucket/path
        if (url != null && url.contains("/storage/v1/object/public/")) {
            String[] parts = url.split("/storage/v1/object/public/");
            if (parts.length > 1) {
                String[] pathParts = parts[1].split("/", 2);
                if (pathParts.length > 1) {
                    return pathParts[1]; // Return path without bucket name
                }
            }
        }
        return null;
    }
}
