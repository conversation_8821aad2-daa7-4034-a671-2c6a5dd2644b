package com.BE.model.response;

import com.BE.enums.StatusEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TagResponse {
    Long id;
    String name;
    StatusEnum status;
    LocalDateTime createdAt;
    LocalDateTime updatedAt;
    Integer usageCount; // Number of resources using this tag
}
