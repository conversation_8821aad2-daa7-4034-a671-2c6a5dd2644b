package com.BE.model.response;

import com.BE.enums.StatusEnum;
import com.BE.model.entity.LessonPlanNode;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LessonPlanNodeResponse {
    Long id;
    Long lessonId;
    String lessonName;
    Long parentId;
    String parentTitle;
    LessonPlanNode.NodeTypeEnum type;
    String title;
    String content;
    Integer orderIndex;
    StatusEnum status;
    LocalDateTime createdAt;
    LocalDateTime updatedAt;
    List<LessonPlanNodeResponse> children; // Child nodes
    List<ResourceResponse> resources; // Linked resources
    Integer resourceCount; // Number of linked resources
}
