package com.BE.service.implementServices;

import com.BE.enums.StatusEnum;
import com.BE.exception.exceptions.BadRequestException;
import com.BE.exception.exceptions.NotFoundException;
import com.BE.mapper.LessonPlanNodeMapper;
import com.BE.model.entity.Lesson;
import com.BE.model.entity.LessonPlanNode;
import com.BE.model.request.LessonPlanNodeRequest;
import com.BE.model.response.LessonPlanNodeResponse;
import com.BE.repository.LessonPlanNodeRepository;
import com.BE.repository.LessonRepository;
import com.BE.repository.ResourceNodeLinkRepository;
import com.BE.service.interfaceServices.ILessonPlanNodeService;
import com.BE.utils.DateNowUtils;
import com.BE.utils.PageUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class LessonPlanNodeServiceImpl implements ILessonPlanNodeService {

    LessonPlanNodeRepository lessonPlanNodeRepository;
    LessonRepository lessonRepository;
    ResourceNodeLinkRepository resourceNodeLinkRepository;
    LessonPlanNodeMapper lessonPlanNodeMapper;
    DateNowUtils dateNowUtils;
    PageUtil pageUtil;

    @Override
    @Transactional
    public LessonPlanNodeResponse createNode(LessonPlanNodeRequest request) {
        // Validate lesson exists
        Lesson lesson = lessonRepository.findById(request.getLessonId())
                .orElseThrow(() -> new NotFoundException("Lesson not found with ID: " + request.getLessonId()));

        // Validate parent if provided
        LessonPlanNode parent = null;
        if (request.getParentId() != null) {
            parent = lessonPlanNodeRepository.findById(request.getParentId())
                    .orElseThrow(
                            () -> new NotFoundException("Parent node not found with ID: " + request.getParentId()));

            // Ensure parent belongs to the same lesson
            if (parent.getLesson().getId() != request.getLessonId()) {
                throw new BadRequestException("Parent node must belong to the same lesson");
            }
        }

        // Check for duplicate title in the same lesson
        if (lessonPlanNodeRepository.findByTitleAndLessonId(request.getTitle().trim(), request.getLessonId())
                .isPresent()) {
            throw new BadRequestException("Node with title '" + request.getTitle() + "' already exists in this lesson");
        }

        // Calculate order index if not provided
        Integer orderIndex = request.getOrderIndex();
        if (orderIndex == null) {
            orderIndex = lessonPlanNodeRepository.findNextOrderIndex(request.getLessonId(), request.getParentId());
        }

        // Create node
        LessonPlanNode node = new LessonPlanNode();
        node.setLesson(lesson);
        node.setParent(parent);
        node.setType(request.getType());
        node.setTitle(request.getTitle().trim());
        node.setContent(request.getContent());
        node.setOrderIndex(orderIndex);
        node.setStatus(StatusEnum.ACTIVE);
        node.setCreatedAt(dateNowUtils.dateNow());
        node.setUpdatedAt(dateNowUtils.dateNow());

        node = lessonPlanNodeRepository.save(node);
        return lessonPlanNodeMapper.toLessonPlanNodeResponse(node);
    }

    @Override
    public List<LessonPlanNodeResponse> getNodesByLesson(Long lessonId, StatusEnum status) {
        List<LessonPlanNode> nodes = lessonPlanNodeRepository.findByLessonIdAndStatusOrderByOrderIndex(
                lessonId, status != null ? status : StatusEnum.ACTIVE);
        return nodes.stream()
                .map(lessonPlanNodeMapper::toLessonPlanNodeResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<LessonPlanNodeResponse> getRootNodesByLesson(Long lessonId, StatusEnum status) {
        List<LessonPlanNode> nodes = lessonPlanNodeRepository.findByLessonIdAndParentIsNullAndStatusOrderByOrderIndex(
                lessonId, status != null ? status : StatusEnum.ACTIVE);
        return nodes.stream()
                .map(lessonPlanNodeMapper::toLessonPlanNodeResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<LessonPlanNodeResponse> getChildNodesByParent(Long parentId, StatusEnum status) {
        List<LessonPlanNode> nodes = lessonPlanNodeRepository.findByParentIdAndStatusOrderByOrderIndex(
                parentId, status != null ? status : StatusEnum.ACTIVE);
        return nodes.stream()
                .map(lessonPlanNodeMapper::toLessonPlanNodeResponse)
                .collect(Collectors.toList());
    }

    @Override
    public LessonPlanNodeResponse getNodeById(Long id) {
        LessonPlanNode node = lessonPlanNodeRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Lesson plan node not found with ID: " + id));
        return lessonPlanNodeMapper.toLessonPlanNodeResponse(node);
    }

    @Override
    @Transactional
    public LessonPlanNodeResponse updateNode(Long id, LessonPlanNodeRequest request) {
        LessonPlanNode node = lessonPlanNodeRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Lesson plan node not found with ID: " + id));

        // Check for duplicate title (excluding current node)
        if (lessonPlanNodeRepository.findByTitleAndLessonIdAndIdNot(
                request.getTitle().trim(), node.getLesson().getId(), id).isPresent()) {
            throw new BadRequestException("Node with title '" + request.getTitle() + "' already exists in this lesson");
        }

        // Update fields
        node.setType(request.getType());
        node.setTitle(request.getTitle().trim());
        node.setContent(request.getContent());
        if (request.getOrderIndex() != null) {
            node.setOrderIndex(request.getOrderIndex());
        }
        node.setUpdatedAt(dateNowUtils.dateNow());

        node = lessonPlanNodeRepository.save(node);
        return lessonPlanNodeMapper.toLessonPlanNodeResponse(node);
    }

    @Override
    @Transactional
    public LessonPlanNodeResponse changeNodeStatus(Long id, StatusEnum status) {
        LessonPlanNode node = lessonPlanNodeRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Lesson plan node not found with ID: " + id));

        node.setStatus(status);
        node.setUpdatedAt(dateNowUtils.dateNow());

        node = lessonPlanNodeRepository.save(node);
        return lessonPlanNodeMapper.toLessonPlanNodeResponse(node);
    }

    @Override
    @Transactional
    public void deleteNode(Long id) {
        LessonPlanNode node = lessonPlanNodeRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Lesson plan node not found with ID: " + id));

        // Check if node has resources linked
        if (resourceNodeLinkRepository.existsByNodeId(id)) {
            throw new BadRequestException(
                    "Cannot delete node that has linked resources. Please unlink resources first.");
        }

        // Check if node has children
        List<LessonPlanNode> children = lessonPlanNodeRepository.findByParentIdAndStatusOrderByOrderIndex(id,
                StatusEnum.ACTIVE);
        if (!children.isEmpty()) {
            throw new BadRequestException(
                    "Cannot delete node that has child nodes. Please delete or move child nodes first.");
        }

        lessonPlanNodeRepository.delete(node);
    }

    @Override
    public Page<LessonPlanNodeResponse> searchNodes(Long lessonId, String search,
            LessonPlanNode.NodeTypeEnum type, StatusEnum status,
            int page, int size, String sortBy, String sortDirection) {
        Pageable pageable = pageUtil.getPageable(page, size, sortBy, sortDirection);
        Page<LessonPlanNode> nodes = lessonPlanNodeRepository.findNodesWithFilters(
                lessonId, search, type, status != null ? status : StatusEnum.ACTIVE, pageable);
        return nodes.map(lessonPlanNodeMapper::toLessonPlanNodeResponse);
    }

    @Override
    public List<LessonPlanNodeResponse> getNodesWithResources(Long lessonId, StatusEnum status) {
        List<LessonPlanNode> nodes = lessonPlanNodeRepository.findNodesWithResourcesByLessonId(
                lessonId, status != null ? status : StatusEnum.ACTIVE);
        return nodes.stream()
                .map(lessonPlanNodeMapper::toLessonPlanNodeResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<LessonPlanNodeResponse> getNodesWithoutResources(Long lessonId, StatusEnum status) {
        List<LessonPlanNode> nodes = lessonPlanNodeRepository.findNodesWithoutResourcesByLessonId(
                lessonId, status != null ? status : StatusEnum.ACTIVE);
        return nodes.stream()
                .map(lessonPlanNodeMapper::toLessonPlanNodeResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void reorderNodes(Long lessonId, List<Long> nodeIds) {
        // Validate all nodes belong to the lesson
        for (int i = 0; i < nodeIds.size(); i++) {
            Long nodeId = nodeIds.get(i);
            LessonPlanNode node = lessonPlanNodeRepository.findById(nodeId)
                    .orElseThrow(() -> new NotFoundException("Node not found with ID: " + nodeId));

            if (node.getLesson().getId() != lessonId) {
                throw new BadRequestException("Node " + nodeId + " does not belong to lesson " + lessonId);
            }

            // Update order index
            node.setOrderIndex(i + 1);
            node.setUpdatedAt(dateNowUtils.dateNow());
            lessonPlanNodeRepository.save(node);
        }
    }

    @Override
    @Transactional
    public LessonPlanNodeResponse moveNode(Long nodeId, Long newParentId, Integer newOrderIndex) {
        LessonPlanNode node = lessonPlanNodeRepository.findById(nodeId)
                .orElseThrow(() -> new NotFoundException("Node not found with ID: " + nodeId));

        // Validate new parent if provided
        LessonPlanNode newParent = null;
        if (newParentId != null) {
            newParent = lessonPlanNodeRepository.findById(newParentId)
                    .orElseThrow(() -> new NotFoundException("Parent node not found with ID: " + newParentId));

            // Ensure new parent belongs to the same lesson
            if (newParent.getLesson().getId() != node.getLesson().getId()) {
                throw new BadRequestException("New parent node must belong to the same lesson");
            }

            // Prevent circular reference
            if (isDescendant(newParent, node)) {
                throw new BadRequestException("Cannot move node to its own descendant");
            }
        }

        // Calculate new order index if not provided
        if (newOrderIndex == null) {
            newOrderIndex = lessonPlanNodeRepository.findNextOrderIndex(node.getLesson().getId(), newParentId);
        }

        // Update node
        node.setParent(newParent);
        node.setOrderIndex(newOrderIndex);
        node.setUpdatedAt(dateNowUtils.dateNow());

        node = lessonPlanNodeRepository.save(node);
        return lessonPlanNodeMapper.toLessonPlanNodeResponse(node);
    }

    @Override
    public Object getNodeStatistics(Long lessonId) {
        long totalNodes = lessonPlanNodeRepository.countByLessonIdAndStatus(lessonId, StatusEnum.ACTIVE);
        List<LessonPlanNode> nodesWithResources = lessonPlanNodeRepository.findNodesWithResourcesByLessonId(lessonId,
                StatusEnum.ACTIVE);
        List<LessonPlanNode> nodesWithoutResources = lessonPlanNodeRepository
                .findNodesWithoutResourcesByLessonId(lessonId, StatusEnum.ACTIVE);

        return new Object[] {
                totalNodes,
                nodesWithResources.size(),
                nodesWithoutResources.size()
        };
    }

    private boolean isDescendant(LessonPlanNode potentialAncestor, LessonPlanNode node) {
        LessonPlanNode current = potentialAncestor.getParent();
        while (current != null) {
            if (current.getId().equals(node.getId())) {
                return true;
            }
            current = current.getParent();
        }
        return false;
    }
}
