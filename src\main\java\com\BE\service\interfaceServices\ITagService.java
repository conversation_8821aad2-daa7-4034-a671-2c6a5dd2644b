package com.BE.service.interfaceServices;

import com.BE.enums.StatusEnum;
import com.BE.model.request.TagRequest;
import com.BE.model.response.TagResponse;
import org.springframework.data.domain.Page;

import java.util.List;

public interface ITagService {

    // Create tag
    TagResponse createTag(TagRequest request);

    // Get all tags with filters
    Page<TagResponse> getAllTags(int page, int size, String search, StatusEnum status, String sortBy, String sortDirection);

    // Get tag by ID
    TagResponse getTagById(Long id);

    // Update tag
    TagResponse updateTag(Long id, TagRequest request);

    // Change tag status
    TagResponse changeTagStatus(Long id, StatusEnum status);

    // Delete tag
    void deleteTag(Long id);

    // Get tag suggestions for autocomplete
    List<TagResponse> getTagSuggestions(String prefix, int limit);

    // Get most used tags
    List<Object[]> getMostUsedTags(int limit);

    // Get tags by resource
    List<TagResponse> getTagsByResource(Long resourceId);

    // Get unused tags
    List<TagResponse> getUnusedTags();

    // Get tag statistics
    Object getTagStatistics();
}
