package com.BE.service.implementServices;

import com.BE.enums.StatusEnum;
import com.BE.exception.exceptions.BadRequestException;
import com.BE.exception.exceptions.NotFoundException;
import com.BE.mapper.TagMapper;
import com.BE.model.entity.Tag;
import com.BE.model.request.TagRequest;
import com.BE.model.response.TagResponse;
import com.BE.repository.ResourceTagRepository;
import com.BE.repository.TagRepository;
import com.BE.service.interfaceServices.ITagService;
import com.BE.utils.DateNowUtils;
import com.BE.utils.PageUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class TagServiceImpl implements ITagService {

    TagRepository tagRepository;
    ResourceTagRepository resourceTagRepository;
    TagMapper tagMapper;
    DateNowUtils dateNowUtils;
    PageUtil pageUtil;

    @Override
    @Transactional
    public TagResponse createTag(TagRequest request) {
        // Check for duplicate name
        if (tagRepository.findByNameIgnoreCase(request.getName().trim()).isPresent()) {
            throw new BadRequestException("Tag with name '" + request.getName() + "' already exists");
        }

        Tag tag = new Tag();
        tag.setName(request.getName().trim());
        tag.setStatus(StatusEnum.ACTIVE);
        tag.setCreatedAt(dateNowUtils.dateNow());
        tag.setUpdatedAt(dateNowUtils.dateNow());

        tag = tagRepository.save(tag);
        return tagMapper.toTagResponse(tag);
    }

    @Override
    public Page<TagResponse> getAllTags(int page, int size, String search, StatusEnum status, String sortBy,
            String sortDirection) {
        Pageable pageable = pageUtil.getPageable(page, size, sortBy, sortDirection);
        Page<Tag> tags = tagRepository.findTagsWithFilters(search, status != null ? status : StatusEnum.ACTIVE,
                pageable);
        return tags.map(tagMapper::toTagResponse);
    }

    @Override
    public TagResponse getTagById(Long id) {
        Tag tag = tagRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Tag not found with ID: " + id));
        return tagMapper.toTagResponse(tag);
    }

    @Override
    @Transactional
    public TagResponse updateTag(Long id, TagRequest request) {
        Tag tag = tagRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Tag not found with ID: " + id));

        // Check for duplicate name (excluding current tag)
        if (tagRepository.findByNameIgnoreCaseAndIdNot(request.getName().trim(), id).isPresent()) {
            throw new BadRequestException("Tag with name '" + request.getName() + "' already exists");
        }

        tag.setName(request.getName().trim());
        tag.setUpdatedAt(dateNowUtils.dateNow());

        tag = tagRepository.save(tag);
        return tagMapper.toTagResponse(tag);
    }

    @Override
    @Transactional
    public TagResponse changeTagStatus(Long id, StatusEnum status) {
        Tag tag = tagRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Tag not found with ID: " + id));

        tag.setStatus(status);
        tag.setUpdatedAt(dateNowUtils.dateNow());

        tag = tagRepository.save(tag);
        return tagMapper.toTagResponse(tag);
    }

    @Override
    @Transactional
    public void deleteTag(Long id) {
        Tag tag = tagRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Tag not found with ID: " + id));

        // Check if tag is used by any resources
        long usageCount = resourceTagRepository.countByTagId(id);
        if (usageCount > 0) {
            throw new BadRequestException("Cannot delete tag that is used by " + usageCount
                    + " resource(s). Please remove the tag from all resources first.");
        }

        tagRepository.delete(tag);
    }

    @Override
    public List<TagResponse> getTagSuggestions(String prefix, int limit) {
        List<Tag> tags = tagRepository.findByNameStartingWithIgnoreCase(prefix, StatusEnum.ACTIVE);
        return tags.stream()
                .limit(limit)
                .map(tagMapper::toTagResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<Object[]> getMostUsedTags(int limit) {
        Pageable pageable = PageUtil.createPageable(0, limit, "usage_count", "desc");
        return tagRepository.findMostUsedTags(StatusEnum.ACTIVE, pageable);
    }

    @Override
    public List<TagResponse> getTagsByResource(Long resourceId) {
        List<Tag> tags = tagRepository.findByResourceId(resourceId, StatusEnum.ACTIVE);
        return tags.stream()
                .map(tagMapper::toTagResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<TagResponse> getUnusedTags() {
        List<Tag> tags = tagRepository.findUnusedTags(StatusEnum.ACTIVE);
        return tags.stream()
                .map(tagMapper::toTagResponse)
                .collect(Collectors.toList());
    }

    @Override
    public Object getTagStatistics() {
        long totalTags = tagRepository.countByStatus(StatusEnum.ACTIVE);
        List<Tag> unusedTags = tagRepository.findUnusedTags(StatusEnum.ACTIVE);

        return new Object[] {
                totalTags,
                unusedTags.size(),
                totalTags - unusedTags.size() // used tags count
        };
    }
}
