package com.BE.mapper;

import com.BE.model.entity.LessonPlanNode;
import com.BE.model.entity.ResourceNodeLink;
import com.BE.model.response.LessonPlanNodeResponse;
import com.BE.model.response.ResourceResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring", uses = {ResourceMapper.class})
public interface LessonPlanNodeMapper {

    @Mapping(source = "lesson.id", target = "lessonId")
    @Mapping(source = "lesson.name", target = "lessonName")
    @Mapping(source = "parent.id", target = "parentId")
    @Mapping(source = "parent.title", target = "parentTitle")
    @Mapping(source = "resourceLinks", target = "resources", qualifiedByName = "mapResourceLinks")
    @Mapping(source = "resourceLinks", target = "resourceCount", qualifiedByName = "mapResourceCount")
    @Mapping(target = "children", ignore = true) // Will be set manually to avoid circular references
    LessonPlanNodeResponse toLessonPlanNodeResponse(LessonPlanNode node);

    @Named("mapResourceLinks")
    default List<ResourceResponse> mapResourceLinks(List<ResourceNodeLink> resourceLinks) {
        if (resourceLinks == null) {
            return null;
        }
        return resourceLinks.stream()
                .map(link -> {
                    ResourceResponse response = new ResourceResponse();
                    response.setId(link.getResource().getId());
                    response.setType(link.getResource().getType());
                    response.setName(link.getResource().getName());
                    response.setDescription(link.getResource().getDescription());
                    response.setUrl(link.getResource().getUrl());
                    response.setFileSize(link.getResource().getFileSize());
                    response.setMimeType(link.getResource().getMimeType());
                    response.setOriginalFilename(link.getResource().getOriginalFilename());
                    response.setThumbnailUrl(link.getResource().getThumbnailUrl());
                    response.setStatus(link.getResource().getStatus());
                    response.setCreatedById(link.getResource().getCreatedBy().getId());
                    response.setCreatedByName(link.getResource().getCreatedBy().getName());
                    response.setCreatedAt(link.getResource().getCreatedAt());
                    response.setUpdatedAt(link.getResource().getUpdatedAt());
                    return response;
                })
                .collect(Collectors.toList());
    }

    @Named("mapResourceCount")
    default Integer mapResourceCount(List<ResourceNodeLink> resourceLinks) {
        return resourceLinks != null ? resourceLinks.size() : 0;
    }
}
