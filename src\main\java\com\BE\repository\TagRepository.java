package com.BE.repository;

import com.BE.enums.StatusEnum;
import com.BE.model.entity.Tag;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TagRepository extends JpaRepository<Tag, Long> {

    // Find tag by name (case-insensitive)
    Optional<Tag> findByNameIgnoreCase(String name);

    // Find tag by name and exclude specific ID (for updates)
    Optional<Tag> findByNameIgnoreCaseAndIdNot(String name, Long id);

    // Search tags with pagination
    @Query("SELECT t FROM Tag t WHERE " +
            "(:search IS NULL OR LOWER(t.name) LIKE LOWER(CONCAT('%', :search, '%'))) AND " +
            "(:status IS NULL OR t.status = :status)")
    Page<Tag> findTagsWithFilters(
            @Param("search") String search,
            @Param("status") StatusEnum status,
            Pageable pageable);

    // Autocomplete suggestions for tag names
    @Query("SELECT t FROM Tag t WHERE LOWER(t.name) LIKE LOWER(CONCAT(:prefix, '%')) AND t.status = :status ORDER BY t.name")
    List<Tag> findByNameStartingWithIgnoreCase(@Param("prefix") String prefix, @Param("status") StatusEnum status);

    // Find most used tags
    @Query("SELECT t, COUNT(rt) as usage_count FROM Tag t LEFT JOIN t.resourceTags rt WHERE t.status = :status GROUP BY t ORDER BY usage_count DESC")
    List<Object[]> findMostUsedTags(@Param("status") StatusEnum status, Pageable pageable);

    // Find tags used by specific resource
    @Query("SELECT t FROM Tag t JOIN t.resourceTags rt WHERE rt.resource.id = :resourceId AND t.status = :status")
    List<Tag> findByResourceId(@Param("resourceId") Long resourceId, @Param("status") StatusEnum status);

    // Find unused tags
    @Query("SELECT t FROM Tag t WHERE t.resourceTags IS EMPTY AND t.status = :status")
    List<Tag> findUnusedTags(@Param("status") StatusEnum status);

    // Count tags by status
    long countByStatus(StatusEnum status);
}
