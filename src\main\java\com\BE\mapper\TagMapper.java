package com.BE.mapper;

import com.BE.model.entity.Tag;
import com.BE.model.response.TagResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TagMapper {

    @Mapping(source = "resourceTags", target = "usageCount", qualifiedByName = "mapUsageCount")
    TagResponse toTagResponse(Tag tag);

    @Named("mapUsageCount")
    default Integer mapUsageCount(List<?> resourceTags) {
        return resourceTags != null ? resourceTags.size() : 0;
    }
}
