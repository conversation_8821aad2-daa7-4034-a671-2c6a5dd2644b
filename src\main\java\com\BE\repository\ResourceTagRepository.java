package com.BE.repository;

import com.BE.model.entity.ResourceTag;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
public interface ResourceTagRepository extends JpaRepository<ResourceTag, Long> {

    // Find by resource and tag
    Optional<ResourceTag> findByResourceIdAndTagId(Long resourceId, Long tagId);

    // Find all tags for a resource
    List<ResourceTag> findByResourceId(Long resourceId);

    // Find all resources for a tag
    List<ResourceTag> findByTagId(Long tagId);

    // Delete by resource and tag
    @Modifying
    @Transactional
    @Query("DELETE FROM ResourceTag rt WHERE rt.resource.id = :resourceId AND rt.tag.id = :tagId")
    void deleteByResourceIdAndTagId(@Param("resourceId") Long resourceId, @Param("tagId") Long tagId);

    // Delete all tags for a resource
    @Modifying
    @Transactional
    void deleteByResourceId(Long resourceId);

    // Delete all resources for a tag
    @Modifying
    @Transactional
    void deleteByTagId(Long tagId);

    // Count resources for a tag
    long countByTagId(Long tagId);

    // Count tags for a resource
    long countByResourceId(Long resourceId);
}
