package com.BE.repository;

import com.BE.enums.StatusEnum;
import com.BE.model.entity.LessonPlanNode;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LessonPlanNodeRepository extends JpaRepository<LessonPlanNode, Long> {

    // Find nodes by lesson ID
    List<LessonPlanNode> findByLessonIdAndStatusOrderByOrderIndex(Long lessonId, StatusEnum status);

    // Find root nodes (no parent) by lesson
    List<LessonPlanNode> findByLessonIdAndParentIsNullAndStatusOrderByOrderIndex(Long lessonId, StatusEnum status);

    // Find child nodes by parent ID
    List<LessonPlanNode> findByParentIdAndStatusOrderByOrderIndex(Long parentId, StatusEnum status);

    // Find node by title and lesson (to prevent duplicates)
    Optional<LessonPlanNode> findByTitleAndLessonId(String title, Long lessonId);

    // Find node by title, lesson and exclude specific ID (for updates)
    Optional<LessonPlanNode> findByTitleAndLessonIdAndIdNot(String title, Long lessonId, Long id);

    // Search nodes with filters
    @Query("SELECT n FROM LessonPlanNode n WHERE " +
            "(:lessonId IS NULL OR n.lesson.id = :lessonId) AND " +
            "(:search IS NULL OR LOWER(n.title) LIKE LOWER(CONCAT('%', :search, '%')) OR LOWER(n.content) LIKE LOWER(CONCAT('%', :search, '%'))) AND " +
            "(:type IS NULL OR n.type = :type) AND " +
            "(:status IS NULL OR n.status = :status)")
    Page<LessonPlanNode> findNodesWithFilters(
            @Param("lessonId") Long lessonId,
            @Param("search") String search,
            @Param("type") LessonPlanNode.NodeTypeEnum type,
            @Param("status") StatusEnum status,
            Pageable pageable);

    // Find nodes with resources
    @Query("SELECT DISTINCT n FROM LessonPlanNode n JOIN n.resourceLinks rl WHERE n.lesson.id = :lessonId AND n.status = :status")
    List<LessonPlanNode> findNodesWithResourcesByLessonId(@Param("lessonId") Long lessonId, @Param("status") StatusEnum status);

    // Find nodes without resources
    @Query("SELECT n FROM LessonPlanNode n WHERE n.lesson.id = :lessonId AND n.resourceLinks IS EMPTY AND n.status = :status")
    List<LessonPlanNode> findNodesWithoutResourcesByLessonId(@Param("lessonId") Long lessonId, @Param("status") StatusEnum status);

    // Count nodes by lesson
    long countByLessonIdAndStatus(Long lessonId, StatusEnum status);

    // Find next order index for a lesson
    @Query("SELECT COALESCE(MAX(n.orderIndex), 0) + 1 FROM LessonPlanNode n WHERE n.lesson.id = :lessonId AND (:parentId IS NULL AND n.parent IS NULL OR n.parent.id = :parentId)")
    Integer findNextOrderIndex(@Param("lessonId") Long lessonId, @Param("parentId") Long parentId);
}
