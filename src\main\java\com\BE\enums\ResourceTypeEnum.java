package com.BE.enums;

/**
 * Enum representing different types of learning resources
 */
public enum ResourceTypeEnum {
    IMAGE("image"),
    VIDEO("video"), 
    GIF("gif"),
    WEBP("webp"),
    IFRAME("iframe");

    private final String value;

    ResourceTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}
