package com.BE.service.interfaceServices;

import com.BE.enums.ResourceTypeEnum;
import com.BE.enums.StatusEnum;
import com.BE.model.request.ResourceRequest;
import com.BE.model.request.ResourceUpdateRequest;
import com.BE.model.response.ResourceResponse;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.UUID;

public interface IResourceService {

    // File upload and resource creation
    ResourceResponse uploadResource(MultipartFile file, ResourceRequest request);
    
    // External link/iframe resource creation
    ResourceResponse createExternalResource(ResourceRequest request);

    // Get resources with filters
    Page<ResourceResponse> getAllResources(int page, int size, String search, ResourceTypeEnum type, 
                                         StatusEnum status, UUID createdBy, String sortBy, String sortDirection);

    // Get resource by ID
    ResourceResponse getResourceById(Long id);

    // Update resource metadata
    ResourceResponse updateResource(Long id, ResourceUpdateRequest request);

    // Update resource file
    ResourceResponse updateResourceFile(Long id, MultipartFile file);

    // Change resource status
    ResourceResponse changeResourceStatus(Long id, StatusEnum status);

    // Delete resource
    void deleteResource(Long id);

    // Get resources by tag
    Page<ResourceResponse> getResourcesByTag(Long tagId, int page, int size, StatusEnum status, String sortBy, String sortDirection);

    // Get resources by multiple tags
    Page<ResourceResponse> getResourcesByTags(List<Long> tagIds, int page, int size, StatusEnum status, String sortBy, String sortDirection);

    // Get unused resources
    Page<ResourceResponse> getUnusedResources(int page, int size, StatusEnum status, String sortBy, String sortDirection);

    // Get resources by lesson
    Page<ResourceResponse> getResourcesByLesson(Long lessonId, int page, int size, StatusEnum status, String sortBy, String sortDirection);

    // Get resources by creator
    Page<ResourceResponse> getResourcesByCreator(UUID createdBy, int page, int size, StatusEnum status, String sortBy, String sortDirection);

    // Get resource statistics
    List<Object[]> getResourceStatistics();

    // Add tags to resource
    ResourceResponse addTagsToResource(Long resourceId, List<Long> tagIds);

    // Remove tags from resource
    ResourceResponse removeTagsFromResource(Long resourceId, List<Long> tagIds);

    // Link resource to node
    void linkResourceToNode(Long resourceId, Long nodeId, String note);

    // Unlink resource from node
    void unlinkResourceFromNode(Long resourceId, Long nodeId);

    // Get resource usage information
    List<Object> getResourceUsage(Long resourceId);
}
