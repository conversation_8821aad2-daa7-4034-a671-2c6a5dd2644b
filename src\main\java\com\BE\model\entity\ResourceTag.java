package com.BE.model.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Entity
@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "resource_tag")
public class ResourceTag {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;

    @ManyToOne
    @JoinColumn(name = "resource_id", nullable = false)
    @JsonIgnore
    Resource resource;

    @ManyToOne
    @JoinColumn(name = "tag_id", nullable = false)
    @JsonIgnore
    Tag tag;

    String createdAt;
}
