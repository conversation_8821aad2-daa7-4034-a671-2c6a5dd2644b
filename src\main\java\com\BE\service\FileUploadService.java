package com.BE.service;

import com.BE.config.SupabaseConfig;
import com.BE.exception.exceptions.BadRequestException;
import com.BE.model.dto.FileUploadResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class FileUploadService {

    private final SupabaseConfig supabaseConfig;
    private final OkHttpClient httpClient;

    // Allowed file types
    private static final List<String> ALLOWED_IMAGE_TYPES = Arrays.asList("jpg", "jpeg", "png", "gif", "webp");
    private static final List<String> ALLOWED_VIDEO_TYPES = Arrays.asList("mp4", "avi", "mov", "wmv", "flv", "webm");
    private static final List<String> ALLOWED_MIME_TYPES = Arrays.asList(
            "image/jpeg", "image/png", "image/gif", "image/webp",
            "video/mp4", "video/avi", "video/quicktime", "video/x-ms-wmv", "video/x-flv", "video/webm"
    );

    // File size limits (in bytes)
    private static final long MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
    private static final long MAX_VIDEO_SIZE = 100 * 1024 * 1024; // 100MB

    public FileUploadResult uploadFile(MultipartFile file, String folder) {
        try {
            // Validate file
            validateFile(file);

            // Generate unique filename
            String originalFilename = file.getOriginalFilename();
            String extension = FilenameUtils.getExtension(originalFilename);
            String uniqueFilename = generateUniqueFilename(originalFilename, extension);
            String fullPath = folder + "/" + uniqueFilename;

            // Upload to Supabase Storage
            String publicUrl = uploadToSupabase(file, fullPath);

            // Create result
            return FileUploadResult.builder()
                    .originalFilename(originalFilename)
                    .filename(uniqueFilename)
                    .path(fullPath)
                    .publicUrl(publicUrl)
                    .fileSize(file.getSize())
                    .mimeType(file.getContentType())
                    .build();

        } catch (Exception e) {
            log.error("Error uploading file: {}", e.getMessage(), e);
            throw new BadRequestException("Failed to upload file: " + e.getMessage());
        }
    }

    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BadRequestException("File is required");
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new BadRequestException("Invalid filename");
        }

        String extension = FilenameUtils.getExtension(originalFilename).toLowerCase();
        String mimeType = file.getContentType();

        // Check file extension
        if (!ALLOWED_IMAGE_TYPES.contains(extension) && !ALLOWED_VIDEO_TYPES.contains(extension)) {
            throw new BadRequestException("File type not allowed. Allowed types: " + 
                String.join(", ", ALLOWED_IMAGE_TYPES) + ", " + String.join(", ", ALLOWED_VIDEO_TYPES));
        }

        // Check MIME type
        if (mimeType == null || !ALLOWED_MIME_TYPES.contains(mimeType)) {
            throw new BadRequestException("Invalid file type");
        }

        // Check file size
        long fileSize = file.getSize();
        if (ALLOWED_IMAGE_TYPES.contains(extension) && fileSize > MAX_IMAGE_SIZE) {
            throw new BadRequestException("Image file size exceeds maximum limit of 10MB");
        }
        if (ALLOWED_VIDEO_TYPES.contains(extension) && fileSize > MAX_VIDEO_SIZE) {
            throw new BadRequestException("Video file size exceeds maximum limit of 100MB");
        }
    }

    private String generateUniqueFilename(String originalFilename, String extension) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        String baseName = FilenameUtils.getBaseName(originalFilename);
        
        // Sanitize filename
        baseName = baseName.replaceAll("[^a-zA-Z0-9._-]", "_");
        
        return String.format("%s_%s_%s.%s", baseName, timestamp, uuid, extension);
    }

    private String uploadToSupabase(MultipartFile file, String path) throws IOException {
        String url = supabaseConfig.getStorageUrl() + "/object/" + supabaseConfig.getStorageBucket() + "/" + path;

        RequestBody requestBody = RequestBody.create(
                file.getBytes(),
                MediaType.parse(file.getContentType())
        );

        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .addHeader("Authorization", "Bearer " + supabaseConfig.getSupabaseKey())
                .addHeader("Content-Type", file.getContentType())
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                log.error("Supabase upload failed: {} - {}", response.code(), errorBody);
                throw new BadRequestException("Failed to upload file to storage: " + errorBody);
            }

            return supabaseConfig.getPublicUrl(path);
        }
    }

    public boolean deleteFile(String path) {
        try {
            String url = supabaseConfig.getStorageUrl() + "/object/" + supabaseConfig.getStorageBucket() + "/" + path;

            Request request = new Request.Builder()
                    .url(url)
                    .delete()
                    .addHeader("Authorization", "Bearer " + supabaseConfig.getSupabaseKey())
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.warn("Failed to delete file from Supabase: {} - {}", response.code(), 
                            response.body() != null ? response.body().string() : "Unknown error");
                    return false;
                }
                return true;
            }
        } catch (Exception e) {
            log.error("Error deleting file from Supabase: {}", e.getMessage(), e);
            return false;
        }
    }
}
