package com.BE.model.entity;

import com.BE.enums.StatusEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.util.HashSet;
import java.util.Set;

@Entity
@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "lesson_plan_node")
public class LessonPlanNode {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;

    @ManyToOne
    @JoinColumn(name = "lesson_id", nullable = false)
    @JsonIgnore
    Lesson lesson;

    @ManyToOne
    @JoinColumn(name = "parent_id")
    @JsonIgnore
    LessonPlanNode parent;

    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL, orphanRemoval = true)
    Set<LessonPlanNode> children = new HashSet<>();

    @Column(nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    NodeTypeEnum type;

    @Column(nullable = false)
    String title;

    @Lob
    @Column(columnDefinition = "TEXT")
    String content;

    @Column(name = "order_index", nullable = false)
    Integer orderIndex;

    @Enumerated(EnumType.STRING)
    StatusEnum status;

    String createdAt;
    String updatedAt;

    @OneToMany(mappedBy = "node", cascade = CascadeType.ALL, orphanRemoval = true)
    Set<ResourceNodeLink> resourceLinks = new HashSet<>();

    /**
     * Enum for different types of lesson plan nodes
     */
    public enum NodeTypeEnum {
        SECTION("section"),
        SUBSECTION("subsection"), 
        LIST_ITEM("list_item"),
        CONTENT_BLOCK("content_block");

        private final String value;

        NodeTypeEnum(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return value;
        }
    }
}
