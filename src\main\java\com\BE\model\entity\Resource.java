package com.BE.model.entity;

import com.BE.enums.ResourceTypeEnum;
import com.BE.enums.StatusEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.util.HashSet;
import java.util.Set;

@Entity
@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Table(name = "resource")
public class Resource {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;

    @Column(nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    ResourceTypeEnum type;

    @Column(nullable = false)
    String name;

    @Lob
    @Column(columnDefinition = "TEXT")
    String description;

    @Lob
    @Column(nullable = false, columnDefinition = "TEXT")
    String url;

    @Column(name = "file_size")
    Long fileSize;

    @Column(name = "mime_type", length = 100)
    String mimeType;

    @Column(name = "original_filename")
    String originalFilename;

    @Column(name = "thumbnail_url", columnDefinition = "TEXT")
    String thumbnailUrl;

    @ManyToOne
    @JoinColumn(name = "created_by", nullable = false)
    @JsonIgnore
    User createdBy;

    @Enumerated(EnumType.STRING)
    StatusEnum status;

    String createdAt;
    String updatedAt;

    @OneToMany(mappedBy = "resource", cascade = CascadeType.ALL, orphanRemoval = true)
    Set<ResourceTag> resourceTags = new HashSet<>();

    @OneToMany(mappedBy = "resource", cascade = CascadeType.ALL, orphanRemoval = true)
    Set<ResourceNodeLink> nodeLinks = new HashSet<>();
}
