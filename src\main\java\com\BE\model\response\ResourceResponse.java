package com.BE.model.response;

import com.BE.enums.ResourceTypeEnum;
import com.BE.enums.StatusEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ResourceResponse {
    Long id;
    ResourceTypeEnum type;
    String name;
    String description;
    String url;
    Long fileSize;
    String mimeType;
    String originalFilename;
    String thumbnailUrl;
    StatusEnum status;
    UUID createdById;
    String createdByName;
    LocalDateTime createdAt;
    LocalDateTime updatedAt;
    List<TagResponse> tags;
    Integer usageCount; // Number of nodes this resource is linked to
}
