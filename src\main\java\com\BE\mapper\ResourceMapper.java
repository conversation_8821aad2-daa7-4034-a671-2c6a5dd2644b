package com.BE.mapper;

import com.BE.model.entity.Resource;
import com.BE.model.entity.ResourceTag;
import com.BE.model.response.ResourceResponse;
import com.BE.model.response.TagResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface ResourceMapper {

    @Mapping(source = "createdBy.id", target = "createdById")
    @Mapping(source = "createdBy.name", target = "createdByName")
    @Mapping(source = "resourceTags", target = "tags", qualifiedByName = "mapResourceTagsToTagResponses")
    @Mapping(source = "nodeLinks", target = "usageCount", qualifiedByName = "mapUsageCount")
    ResourceResponse toResourceResponse(Resource resource);

    @Named("mapResourceTagsToTagResponses")
    default List<TagResponse> mapResourceTagsToTagResponses(List<ResourceTag> resourceTags) {
        if (resourceTags == null) {
            return null;
        }
        return resourceTags.stream()
                .map(resourceTag -> {
                    TagResponse tagResponse = new TagResponse();
                    tagResponse.setId(resourceTag.getTag().getId());
                    tagResponse.setName(resourceTag.getTag().getName());
                    tagResponse.setStatus(resourceTag.getTag().getStatus());
                    tagResponse.setCreatedAt(resourceTag.getTag().getCreatedAt());
                    tagResponse.setUpdatedAt(resourceTag.getTag().getUpdatedAt());
                    return tagResponse;
                })
                .collect(Collectors.toList());
    }

    @Named("mapUsageCount")
    default Integer mapUsageCount(List<?> nodeLinks) {
        return nodeLinks != null ? nodeLinks.size() : 0;
    }
}
