package com.BE.service.interfaceServices;

import com.BE.enums.StatusEnum;
import com.BE.model.entity.LessonPlanNode;
import com.BE.model.request.LessonPlanNodeRequest;
import com.BE.model.response.LessonPlanNodeResponse;
import org.springframework.data.domain.Page;

import java.util.List;

public interface ILessonPlanNodeService {

    // Create lesson plan node
    Lesson<PERSON>lanNodeResponse createNode(LessonPlanNodeRequest request);

    // Get nodes by lesson
    List<LessonPlanNodeResponse> getNodesByLesson(Long lessonId, StatusEnum status);

    // Get root nodes by lesson
    List<LessonPlanNodeResponse> getRootNodesByLesson(Long lessonId, StatusEnum status);

    // Get child nodes by parent
    List<LessonPlanNodeResponse> getChildNodesByParent(Long parentId, StatusEnum status);

    // Get node by ID
    LessonPlanNodeResponse getNodeById(Long id);

    // Update node
    LessonPlanNodeResponse updateNode(Long id, LessonPlanNodeRequest request);

    // Change node status
    LessonPlanNodeResponse changeNodeStatus(Long id, StatusEnum status);

    // Delete node
    void deleteNode(Long id);

    // Search nodes with filters
    Page<LessonPlanNodeResponse> searchNodes(Long lessonId, String search, 
                                           LessonPlanNode.NodeTypeEnum type, StatusEnum status,
                                           int page, int size, String sortBy, String sortDirection);

    // Get nodes with resources
    List<LessonPlanNodeResponse> getNodesWithResources(Long lessonId, StatusEnum status);

    // Get nodes without resources
    List<LessonPlanNodeResponse> getNodesWithoutResources(Long lessonId, StatusEnum status);

    // Reorder nodes
    void reorderNodes(Long lessonId, List<Long> nodeIds);

    // Move node to different parent
    LessonPlanNodeResponse moveNode(Long nodeId, Long newParentId, Integer newOrderIndex);

    // Get node statistics
    Object getNodeStatistics(Long lessonId);
}
